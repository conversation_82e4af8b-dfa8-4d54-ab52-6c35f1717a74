{"name": "Lithuanian Lead Generation Workflow", "nodes": [{"id": "manual-trigger", "name": "Manual Trigger", "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [240, 300], "parameters": {}}, {"id": "apify-scraper", "name": "Apify Google Maps Scraper", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [460, 300], "parameters": {"url": "https://api.apify.com/v2/acts/compass~crawler-google-places/run-sync-get-dataset-items?token=**********************************************", "method": "POST", "sendBody": true, "contentType": "json", "jsonBody": "{\n  \"includeWebResults\": false,\n  \"language\": \"en\",\n  \"locationQuery\": \"Klaipeda, Lithuania\",\n  \"maxCrawledPlacesPerSearch\": 50,\n  \"maxImages\": 0,\n  \"maximumLeadsEnrichmentRecords\": 0,\n  \"scrapeContacts\": false,\n  \"scrapeDirectories\": false,\n  \"scrapeImageAuthors\": false,\n  \"scrapePlaceDetailPage\": false,\n  \"scrapeReviewsPersonalData\": false,\n  \"scrapeTableReservationProvider\": false,\n  \"searchStringsArray\": [\n    \"restaurant\",\n    \"cafe\",\n    \"shop\",\n    \"store\",\n    \"service\",\n    \"business\"\n  ],\n  \"skipClosedPlaces\": false\n}", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}}}, {"id": "filter-no-website", "name": "Filter Businesses Without Website", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [680, 300], "parameters": {"language": "javaScript", "mode": "runOnceForAllItems", "jsCode": "// Filter businesses without websites and format data\nconst businesses = $input.all();\nconst filteredLeads = [];\n\nfor (const item of businesses) {\n  const data = item.json;\n  \n  // Check if business has no website or website is empty/null\n  if (!data.website || data.website === '' || data.website === null) {\n    const lead = {\n      business_name: data.title || data.name || 'Unknown',\n      address: data.address || data.location || 'Unknown',\n      google_phone: data.phone || data.phoneNumber || '',\n      website: '', // Empty since we're filtering for no website\n      owner_phone: '', // Will be filled later from rekvizitai\n      phone_source: 'pending', // pending/owner/google/missing\n      sms_sent: 'pending', // pending/success/failed\n      sms_status: '',\n      reply_received: 'no',\n      notes: 'Lead from Google Maps - no website found',\n      google_maps_url: data.url || '',\n      category: data.categoryName || data.category || '',\n      rating: data.totalScore || data.rating || '',\n      reviews_count: data.reviewsCount || ''\n    };\n    \n    filteredLeads.push(lead);\n  }\n}\n\nconsole.log(`Found ${filteredLeads.length} businesses without websites`);\n\nreturn filteredLeads.map(lead => ({ json: lead }));"}}, {"id": "save-initial-leads", "name": "Save Initial Leads to Google Sheets", "type": "n8n-nodes-base.googleSheets", "typeVersion": 4.6, "position": [900, 300], "parameters": {"operation": "append", "documentId": {"mode": "list", "value": "YOUR_GOOGLE_SHEET_ID_HERE"}, "sheetName": {"mode": "list", "value": "Leads"}, "range": "A:N", "options": {"locationDefine": "specifyRange"}, "columns": {"mappingMode": "defineBelow", "value": {"business_name": "={{ $json.business_name }}", "address": "={{ $json.address }}", "google_phone": "={{ $json.google_phone }}", "website": "={{ $json.website }}", "owner_phone": "={{ $json.owner_phone }}", "phone_source": "={{ $json.phone_source }}", "sms_sent": "={{ $json.sms_sent }}", "sms_status": "={{ $json.sms_status }}", "reply_received": "={{ $json.reply_received }}", "notes": "={{ $json.notes }}", "google_maps_url": "={{ $json.google_maps_url }}", "category": "={{ $json.category }}", "rating": "={{ $json.rating }}", "reviews_count": "={{ $json.reviews_count }}"}}}}, {"id": "batch-leads", "name": "Batch Leads for Processing", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1120, 300], "parameters": {"language": "javaScript", "mode": "runOnceForAllItems", "jsCode": "// Process leads in batches of 10 for rekvizitai scraping\nconst leads = $input.all();\nconst batchSize = 10;\nconst batches = [];\n\nfor (let i = 0; i < leads.length; i += batchSize) {\n  const batch = leads.slice(i, i + batchSize);\n  batches.push({ json: { batch: batch.map(item => item.json), batchNumber: Math.floor(i / batchSize) + 1 } });\n}\n\nconsole.log(`Created ${batches.length} batches of leads`);\nreturn batches;"}}, {"id": "scrape-rekvizitai", "name": "Scrape Rekvizitai for Owner Info", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1340, 300], "parameters": {"language": "javaScript", "mode": "runOnceForEachItem", "jsCode": "// Scrape rekvizitai.vz.lt for owner contact information\nconst batch = $json.batch;\nconst enrichedLeads = [];\n\nfor (const lead of batch) {\n  try {\n    // Create URL-friendly slug from business name\n    const businessSlug = lead.business_name\n      .toLowerCase()\n      .replace(/[^a-z0-9\\s-]/g, '') // Remove special characters\n      .replace(/\\s+/g, '-') // Replace spaces with hyphens\n      .replace(/-+/g, '-') // Replace multiple hyphens with single\n      .trim();\n    \n    const rekvizitaiUrl = `https://rekvizitai.vz.lt/imone/${businessSlug}`;\n    \n    // For now, we'll prepare the structure and add actual HTTP scraping later\n    // This is a placeholder that sets up the data structure\n    const enrichedLead = {\n      ...lead,\n      rekvizitai_url: rekvizitaiUrl,\n      owner_phone: lead.google_phone || '', // Fallback to Google phone for now\n      phone_source: lead.google_phone ? 'google' : 'missing',\n      notes: lead.notes + ` | Rekvizitai URL: ${rekvizitaiUrl}`\n    };\n    \n    enrichedLeads.push(enrichedLead);\n    \n  } catch (error) {\n    console.error(`Error processing lead ${lead.business_name}:`, error);\n    \n    // Fallback to Google phone if rekvizitai fails\n    const enrichedLead = {\n      ...lead,\n      owner_phone: lead.google_phone || '',\n      phone_source: lead.google_phone ? 'google' : 'missing',\n      notes: lead.notes + ' | Rekvizitai scraping failed'\n    };\n    \n    enrichedLeads.push(enrichedLead);\n  }\n}\n\nreturn enrichedLeads.map(lead => ({ json: lead }));"}}, {"id": "update-leads-with-owner-info", "name": "Update Leads with Owner Info", "type": "n8n-nodes-base.googleSheets", "typeVersion": 4.6, "position": [1560, 300], "parameters": {"operation": "appendOrUpdate", "documentId": {"mode": "list", "value": "YOUR_GOOGLE_SHEET_ID_HERE"}, "sheetName": {"mode": "list", "value": "Leads"}, "range": "A:N", "options": {"locationDefine": "specifyRange"}, "columns": {"mappingMode": "defineBelow", "value": {"business_name": "={{ $json.business_name }}", "address": "={{ $json.address }}", "google_phone": "={{ $json.google_phone }}", "website": "={{ $json.website }}", "owner_phone": "={{ $json.owner_phone }}", "phone_source": "={{ $json.phone_source }}", "sms_sent": "={{ $json.sms_sent }}", "sms_status": "={{ $json.sms_status }}", "reply_received": "={{ $json.reply_received }}", "notes": "={{ $json.notes }}", "google_maps_url": "={{ $json.google_maps_url }}", "category": "={{ $json.category }}", "rating": "={{ $json.rating }}", "reviews_count": "={{ $json.reviews_count }}"}}}}], "connections": {"Manual Trigger": {"main": [[{"node": "Apify Google Maps Scraper", "type": "main", "index": 0}]]}, "Apify Google Maps Scraper": {"main": [[{"node": "Filter Businesses Without Website", "type": "main", "index": 0}]]}, "Filter Businesses Without Website": {"main": [[{"node": "Save Initial Leads to Google Sheets", "type": "main", "index": 0}]]}, "Save Initial Leads to Google Sheets": {"main": [[{"node": "Batch Leads for Processing", "type": "main", "index": 0}]]}, "Batch Leads for Processing": {"main": [[{"node": "Scrape Rekvizitai for Owner Info", "type": "main", "index": 0}]]}, "Scrape Rekvizitai for Owner Info": {"main": [[{"node": "Update Leads with Owner Info", "type": "main", "index": 0}]]}}, "settings": {"executionOrder": "v1"}}