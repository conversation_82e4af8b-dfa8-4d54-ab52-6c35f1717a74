{"name": "Apify Test - Simple", "nodes": [{"id": "manual-trigger", "name": "Manual Trigger", "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [240, 300], "parameters": {}}, {"id": "apify-test", "name": "Apify Test", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [460, 300], "parameters": {"url": "https://api.apify.com/v2/acts/compass~crawler-google-places/run-sync-get-dataset-items?token=**********************************************", "method": "POST", "sendBody": true, "contentType": "json", "jsonBody": "{\n  \"searchStringsArray\": [\n    \"restaurant\"\n  ],\n  \"locationQuery\": \"Klaipeda, Lithuania\",\n  \"maxCrawledPlacesPerSearch\": 5,\n  \"language\": \"en\",\n  \"maximumLeadsEnrichmentRecords\": 0,\n  \"maxImages\": 0\n}", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "options": {"timeout": 300000}}}], "connections": {"Manual Trigger": {"main": [[{"node": "Apify Test", "type": "main", "index": 0}]]}}, "settings": {"executionOrder": "v1"}}