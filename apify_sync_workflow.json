{"name": "Apify Sync Test", "nodes": [{"id": "manual-trigger", "name": "Manual Trigger", "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [240, 300], "parameters": {}}, {"id": "apify-sync", "name": "Apify Sync Run", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [460, 300], "parameters": {"url": "https://api.apify.com/v2/acts/compass~crawler-google-places/run-sync?token=**********************************************&timeout=300", "method": "POST", "sendBody": true, "contentType": "json", "jsonBody": "{\n  \"includeWebResults\": false,\n  \"language\": \"en\",\n  \"locationQuery\": \"Klaipeda, Lithuania\",\n  \"maxCrawledPlacesPerSearch\": 5,\n  \"maxImages\": 0,\n  \"maximumLeadsEnrichmentRecords\": 0,\n  \"scrapeContacts\": false,\n  \"scrapeDirectories\": false,\n  \"scrapeImageAuthors\": false,\n  \"scrapePlaceDetailPage\": false,\n  \"scrapeReviewsPersonalData\": false,\n  \"scrapeTableReservationProvider\": false,\n  \"searchStringsArray\": [\n    \"restaurant\"\n  ],\n  \"skipClosedPlaces\": false\n}", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "options": {"timeout": 300000}}}], "connections": {"Manual Trigger": {"main": [[{"node": "Apify Sync Run", "type": "main", "index": 0}]]}}, "settings": {"executionOrder": "v1"}}